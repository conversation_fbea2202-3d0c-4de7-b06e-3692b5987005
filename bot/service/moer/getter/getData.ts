import { JuziAPI } from '../../../lib/juzi/api'
import { Config } from '../../../config/config'
import logger from '../../../model/logger/logger'
import { ChatDB } from '../database/chat'
import { IMoerUserInfo, MoerAPI } from '../../../model/moer_api/moer'
import { ChatStateStore, ChatStatStoreManager } from '../storage/chat_state_store'
import { IScheduleTime, taskTimeToCourseTime } from '../components/schedule/creat_schedule_task'
import { PolyvAPI } from '../../../model/polyv/polyv'
import { StringHelper } from '../../../lib/string'
import { Danmu, DanmuDB } from '../database/danmu'
import { DateHelper } from '../../../lib/date/date'
import { ObjectUtil } from '../../../lib/object'
import { PrismaMongoClient } from '../../../model/mongodb/prisma'
import dayjs from 'dayjs'
import { getChatId } from '../../../config/chat_id'
import { RedisCacheDB } from '../../../model/redis/redis_cache'
import { catchError } from '../../../lib/error/catchError'
import { RegexHelper } from '../../../lib/regex/regex'
import isoWeek from 'dayjs/plugin/isoWeek'
import isBetween from 'dayjs/plugin/isBetween'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import { LocalHolidayConfig } from '../components/pause_calendar/local_holiday_config'


export enum WecomTag {
  AIOn = 'AI on',
  AIOff = 'AI off',
  JoinedGroup = '进群',
}

export interface CourseInfo {
  day: number
  is_recording?: boolean
}

interface ParsedResource {
  userId: string
  sku: string
  vodId?: number
  liveId?: number
  day: number
}

interface CoursePlaybackInfo {
  duration: number
  playbackTime: number
}

export interface CourseListeningStats {
  listeningDuration: number    // 听课时长（秒）
  listeningPercentage: number  // 听课百分比（0-100）
  isCompleted: boolean         // 是否听完课
}

/**
 * 对 JuziAPI, MoerAPI, Config 等的 二次封装，方便获取信息
 */
export class DataService {
  public static async getWechatName(userId: string) {
    try {
      const currentSender = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, userId)
      if (!currentSender) {
        logger.warn('获取客户信息失败', userId)
        return ''
      }

      return currentSender.name ?? ''
    } catch (e) {
      return ''
    }
  }

  public static getCounsellorName() {
    return Config.setting.AGENT_NAME
  }

  /**
   * 获取客户当期课的开课时间
   * @param chatId
   */
  public static async getCourseStartTime(chatId: string) {
    try {
      // 获取客户属于哪一期
      const chat = await ChatDB.getById(chatId)
      if (!chat) {
        throw new Error('无法获取客户信息')
      }
      if (!chat.course_no) {
        throw new Error('无法获取客户课程期数')
      }

      // 获取这一期的开课时间
      const courseInfo =  await this.getCourseInfoByCourseNo(chat.course_no)
      return DateHelper.parseDate(courseInfo.startTime)
    } catch (e) {
      return await DataService.getCourseStartTimeByCourseNo(DataService.getNextWeekCourseNo())
    }
  }

  public static async isInClass (chat_id: string, course: CourseInfo) {
    if (course.day < 0 || course.day > 4) {
      throw new Error (' 课程天数不正确 ')
    }

    if (course.day === 0) { // 小课堂只有录播课
      course.is_recording = true
    }

    const dayStatusMap = {
      1: 'is_in_day1_class',
      2: 'is_in_day2_class',
      3: 'is_in_day3_class',
      4: 'is_in_day4_class'
    }

    // 获取客户状态
    const allState = ChatStateStore.get (chat_id)
    const state = allState.state
    const status = dayStatusMap [course.day]

    // 如果状态已经显示到课，直接返回 true
    if (state [status]) {
      return true
    }

    // 使用通用方法获取课程播放信息
    const playbackInfo = await this.getCoursePlaybackInfo(chat_id, course)

    if (!playbackInfo) {
      return false
    }

    const playBackTime = playbackInfo.playbackTime

    // 判断是否到课（播放时间超过 5 分钟）
    let isInClass = playBackTime >= 5 * 60 // 5 分钟 = 300 秒

    // 小课堂 看了 3min 以上就算到课
    if (course.day === 0) {
      isInClass = playBackTime >= 3 * 60
    }

    // 如果距离开课时间 <= 30 分钟，看过 x 分钟就算到课
    if (new Date().getHours() === 20 && new Date().getMinutes() <= 30) {
      const courseStartTime = new Date().setHours(20, 0, 0, 0)
      const diffMillis =  Date.now() - courseStartTime
      const remainingMinutes = Math.floor(diffMillis / 60000)
      const thresholdMinutes = Math.floor(remainingMinutes / 10)

      isInClass = playBackTime > (thresholdMinutes === 0 ?  2 * 60 : 2 * thresholdMinutes * 60) // playbackTime 必须大于 0, 不能用 >=
    }
    logger.log(`chat_id:${chat_id} 上课时间 day${course.day} 是否录播:${course.is_recording ?? false} 持续时间:${playBackTime}`)

    if (isInClass) {
      // 更新状态
      ChatStateStore.get (chat_id).state [status] = true
    }

    return isInClass
  }


  /**
   * 主动获取课程完成情况，课程可以传 0，1，2，3，4，is_recording
   */
  public static async isCompletedCourse(chat_id: string, course: CourseInfo) {
    if (course.day < 0 || course.day > 4) {
      throw new Error('课程天数不正确')
    }

    const dayStatusMap = {
      0: 'is_complete_pre_course',
      1: {
        course: 'is_complete_day1_course',
        recording: 'is_complete_day1_course_recording'
      },
      2: {
        course: 'is_complete_day2_course',
        recording: 'is_complete_day2_course_recording'
      },
      3: {
        course: 'is_complete_day3_course',
        recording: 'is_complete_day3_course_recording'
      },
      4: {
        course: 'is_complete_day4_course',
        recording: 'is_complete_day4_course_recording'
      }
    }

    // 获取 客户状态
    const allState = ChatStateStore.get (chat_id)
    const state = allState.state
    const status = dayStatusMap[course.day]

    if (course.day === 0 && state[status]) {
      return true
    } else if (status && course.is_recording && state[status.recording]) {
      return true
    } else if (status && state[status.course]) {
      return true
    }

    // 使用通用方法获取课程播放信息
    const playbackInfo = await this.getCoursePlaybackInfo(chat_id, course)

    if (!playbackInfo) {
      return false
    }

    const { duration: courseDuration, playbackTime: playBackTime } = playbackInfo

    logger.log(`chat_id:${chat_id} 上课时间 day${course.day} 是否录播:${course.is_recording ?? false} 持续时间:${playBackTime}`)

    // 小课堂按照 30% 算完课，最后两节课按 40% 算完课，其他按 60% 算完课
    let threshHold = 0.6 // 默认 60%
    if (course.day === 0) {
      threshHold = 0.3 // 小课堂 30%
    } else if (course.day === 3 || course.day === 4) {
      threshHold = 0.4 // 最后两节课 40%
    }

    const isComplete = playBackTime / courseDuration >= threshHold

    if (isComplete) {
      // 更新状态
      if (course.day === 0) {
        ChatStateStore.get(chat_id).state.is_complete_pre_course = true
      } else {
        ChatStateStore.get(chat_id).state[course.is_recording ? status.recording : status.course] = true
      }
    }

    return isComplete
  }

  public static async isAttendCourseMoreThanOneSecond(chat_id: string, course: CourseInfo) {
    if (course.day < 0 || course.day > 4) {
      throw new Error('课程天数不正确')
    }

    const dayStatusMap = {
      0: 'is_attend_pre_course_one_second',
      1: {
        course: 'is_attend_day1_course_one_second',
        recording: 'is_attend_day1_course_recording_one_second'
      },
      2: {
        course: 'is_attend_day2_course_one_second',
        recording: 'is_attend_day2_course_recording_one_second'
      },
      3: {
        course: 'is_attend_day3_course_one_second',
        recording: 'is_attend_day3_course_recording_one_second'
      },
      4: {
        course: 'is_attend_day4_course_one_second',
        recording: 'is_attend_day4_course_recording_one_second'
      }
    }

    // 获取 客户状态
    const allState = ChatStateStore.get (chat_id)
    const state = allState.state
    const status = dayStatusMap[course.day]

    if (course.day === 0 && state[status]) {
      return true
    } else if (status && course.is_recording && state[status.recording]) {
      return true
    } else if (status && state[status.course]) {
      return true
    }

    // 使用通用方法获取课程播放信息
    const playbackInfo = await this.getCoursePlaybackInfo(chat_id, course)

    if (!playbackInfo) {
      return false
    }

    const playBackTime = playbackInfo.playbackTime

    logger.log(`chat_id:${chat_id} 上课时间 day${course.day} 是否录播:${course.is_recording ?? false} 持续时间:${playBackTime}`)

    const isAttendOneSecond = playBackTime >= 1

    if (isAttendOneSecond) {
      // 更新状态
      if (course.day === 0) {
        ChatStateStore.get(chat_id).state[status] = true
      } else {
        ChatStateStore.get(chat_id).state[course.is_recording ? status.recording : status.course] = true
      }
    }

    return isAttendOneSecond
  }

  public static async isInGroup(user_id: string) {
    return await JuziAPI.isInGroup({
      imBotId: Config.setting.wechatConfig?.id as string,
      imRoomId: Config.setting.wechatConfig?.classGroupId as string,
      imContactId: user_id
    })
  }

  /**
   * 是否完成所有课程(含录播课完课)
   */
  public static async isCompletedAllCourse(chat_id: string) {
    const liveStreamCompleteTask: Promise<boolean>[] = []
    const recordingCompleteTask: Promise<boolean>[] = []
    for (let i = 1; i <= 4; i++) {
      liveStreamCompleteTask.push(this.isCompletedCourse(chat_id, { day: i }))
      recordingCompleteTask.push(this.isCompletedCourse(chat_id, { day: i, is_recording: true }))
    }

    const liveStreamComplete = await Promise.all(liveStreamCompleteTask)
    const recordingComplete = await Promise.all(recordingCompleteTask)
    for (let i = 0; i < 4; i++) {
      if (!liveStreamComplete[i] && !recordingComplete[i]) {
        return false
      }
    }
    return true
  }

  /**
   * 获取客户某节课的听课统计信息
   * @param chat_id 聊天ID
   * @param course 课程信息
   * @returns 听课时长、听课百分比和是否听完课
   */
  public static async getCourseListeningStats(chat_id: string, course: CourseInfo): Promise<CourseListeningStats | null> {
    const playbackInfo = await this.getCoursePlaybackInfo(chat_id, course)

    if (!playbackInfo) {
      return null
    }

    const { duration, playbackTime } = playbackInfo

    // 计算听课百分比
    const listeningPercentage = duration > 0 ? Math.round((playbackTime / duration) * 100) : 0

    // 判断是否完成：小课堂按照 30% 算完课，最后两节课按 40% 算完课，其他按 60% 算完课
    let threshHold = 0.6 // 默认 60%
    if (course.day === 0) {
      threshHold = 0.3 // 小课堂 30%
    } else if (course.day === 3 || course.day === 4) {
      threshHold = 0.4 // 最后两节课 40%
    }
    const isCompleted = playbackTime / duration >= threshHold

    logger.log(`chat_id:${chat_id} 课程统计 day${course.day} 是否录播:${course.is_recording ?? false} 听课时长:${playbackTime}秒 课程总时长:${duration}秒 听课百分比:${listeningPercentage}% 是否完成:${isCompleted}`)

    return {
      listeningDuration: playbackTime,
      listeningPercentage: listeningPercentage,
      isCompleted: isCompleted
    }
  }

  /**
   * 获取课程完成情况
   */
  public static async getMoerCourseStatus(course: ParsedResource) {
    return await MoerAPI.getUserChapterStatus({
      userId: course.userId,
      liveId: course.liveId?.toString(),
      vodId: course.vodId?.toString(),
      sku: course.sku
    })
  }


  /**
   * 获取课程信息
   * @param moerId
   */
  public static async getMoerCourses(moerId: string) {
    const resourceMap : {[key: number]: {
        live?: ParsedResource
        record?: ParsedResource
      }} = {}

    // 获取课程列表
    const courses = await MoerAPI.getUserCourses(moerId)

    courses.forEach((course) => {
      const sku = course.sku
      course.resource.forEach((res) => {
        const day = res.day === 5 ? 4 : res.day // 修正 day 为 4

        // 初始化天数的资源对象，如果还未存在
        if (!resourceMap[day]) {
          resourceMap[day] = {}
        }

        if (res.vodId) {
          resourceMap[day].record = {
            userId: moerId,
            sku: sku,
            vodId: res.vodId,
            day: day
          }
        }

        if (res.liveId) {
          resourceMap[day].live = {
            userId: moerId,
            sku: sku,
            liveId: res.liveId,
            day: day
          }
        }
      })
    })

    return resourceMap
  }

  public static async getMoerIdByChatId(chatId: string) {
    const chat =  await ChatDB.getById(chatId)

    if (!chat) {
      return null
    }

    return chat.moer_id
  }

  public static async getChatIdByMoerId(moerId: string) {
    const chat = await ChatDB.getByMoerId(moerId)

    if (!chat) {
      return null
    }

    return chat.id
  }

  public static async getEnergyTestScore(chatId: string): Promise<number | null> {
    let score
    if (ChatStateStore.get(chatId).userSlots.energy_test_score || ChatStateStore.get(chatId).userSlots.energy_test_score === 0) { // 注意，有可能是 0 分
      score = ChatStateStore.get(chatId).userSlots.energy_test_score
    } else {
      const moerId = await DataService.getMoerIdByChatId(chatId)
      if (!moerId) {
        return null
      }

      const scoreList = await MoerAPI.getUserEnergyMark(moerId)
      if (scoreList.data.list && scoreList.data.list.length > 0) {
        score = scoreList.data.list[scoreList.data.list.length - 1].examScore

        ChatStateStore.update(chatId, {
          userSlots: {
            energy_test_score: score
          }
        })
      } else {
        return null
      }
    }

    return score
  }

  /**
   * 获取固定的课程时长（秒）
   * @param day 课程天数
   * @returns 课程时长（秒）
   */
  public static getFixedCourseDuration(day: number): number {
    const durations = {
      0: 14 * 60 + 30,   // 小讲堂：14:30
      1: 60 * 60 + 44 * 60,  // 第一节课：1:44:01
      2: 4475,  // 第二节课：1:14:35
      3: 8502,  // 第三节课：2:21:42
      4: 8666   // 加播课：2:24:26
    }

    return durations[day] || 5400 // 默认1.5小时
  }

  public static async getCurrentTime(chatId: string) {
    try {
      return await taskTimeToCourseTime(new Date(), chatId)
    } catch (e) {
      // 当客户没有绑定课程时，返回当前时间
      return {
        is_course_week: false,
        day: new Date().getDay() === 0 ? 7 : new Date().getDay(),
        time: DateHelper.formatDate(new Date(), 'HH:mm:ss')
      }
    }
  }

  public static async getDanmuByChatId(chatId: string) {
    const moerId = await DataService.getMoerIdByChatId(chatId)
    if (!moerId) {
      return []
    }

    return DanmuDB.getDanmusByMoerId(moerId)
  }

  /**
   * 获取客户在线状态
   */
  static parseCourseNo(moerUser: IMoerUserInfo) {
    let maxStage = -Infinity // Initialize with a very low number to track the max stage found

    for (const item of moerUser.userGoodsSeries) {
      if (item.type === 1 && item.stage > maxStage) {
        maxStage = item.stage
      }
    }

    // If maxStage is still -Infinity, no items with type === 1 were found
    if (maxStage !== -Infinity) {
      return maxStage
    }

    return DataService.getNextWeekCourseNo()
  }

  public static async saveChat(chat_id: string, senderId: string) {
    if (senderId === Config.setting.wechatConfig?.id || chat_id.startsWith('local') ||  chat_id.startsWith('mock')) { // 不创建自己账号的信息
      return
    }

    if (!ChatStateStore.hasState(chat_id)) {
      await ChatStatStoreManager.initState(chat_id)
    }

    if (await ChatDB.getById(chat_id) === null) {
      const currentSender = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, senderId)

      await ChatDB.create({
        id: chat_id,
        round_ids: [],
        contact: {
          wx_id: senderId,
          wx_name: currentSender ? currentSender.name : senderId,
        },
        wx_id: Config.setting.wechatConfig?.id as string,
        created_at: new Date(),
        chat_state: ChatStateStore.get(chat_id)
      })
    } else {
      // 更新下状态
      await ChatDB.updateState(chat_id, ChatStateStore.get(chat_id))
    }
  }

  static async getCourseLinkByCourseNo(course_no: number, day: number, is_recording?: boolean) {
    const courseInfo = await MoerAPI.getCurrentCourseInfo(course_no)
    if (!courseInfo) {
      logger.warn('获取课程信息失败', course_no)
      return ''
    }

    if (courseInfo.code !== 0) {
      logger.warn('获取课程信息失败', course_no)
      return ''
    }

    const course = courseInfo.data.resource.find((item) => item.day === day)
    if (!course) {
      logger.warn('获取课程信息失败', course_no)
      return ''
    }

    if (is_recording) { return course.vodShortUrl }

    return course.liveShortUrl
  }


  static async getPreCourseLinkByCourseNo(course_no: number) {
    const courseInfo = await MoerAPI.getCurrentCourseInfo(course_no)
    if (!courseInfo) {
      logger.warn('获取课程信息失败', course_no)
      return ''
    }

    const course = courseInfo.data.resource.find((item) => item.day === 0)
    if (!course) {
      logger.warn('获取课程信息失败', course_no)
      return ''
    }


    return course.liveShortUrl
  }

  static async getCourseLink(day: number, chat_id: string, is_recording?: boolean) {
    const chat = await ChatDB.getById(chat_id)
    let courseNo = chat?.course_no
    if (!courseNo) {
      courseNo = DataService.getCurrentWeekCourseNo()
    }

    const courseInfo = await MoerAPI.getCurrentCourseInfo(courseNo)
    if (!courseInfo) {
      logger.warn('获取课程信息失败1', chat_id, courseNo)
      return ''
    }

    if (courseInfo.code !== 0) {
      logger.warn('获取课程信息失败2', chat_id, courseNo)
      return ''
    }

    const course = courseInfo.data.resource.find((item) => item.day === day)
    if (!course) {
      logger.warn('获取课程信息失败3', chat_id, courseNo, day)
      return ''
    }

    if (is_recording) {
      return course.vodShortUrl
    }

    return course.liveShortUrl
  }

  static async pullLiveDanmuAndStore (liveId: string, formattedDate: string, formattedDate2: string, courseNo: number, day: number) {
    const data = await PolyvAPI.getLiveDanmu(liveId, formattedDate, formattedDate2)
    // 过滤一下 弹幕
    const filteredData = data.filter((item) => StringHelper.isNumber(item.user.userId))

    const simplifiedData = filteredData.map((item) => {
      return {
        liveId: liveId,
        content: item.content,
        time: item.time,
        userId: item.user.userId,
        userName: item.user.nickname,
        courseNo: courseNo,
        sendTime: new Date(item.time),
        day: day
      }
    }).filter((item) => typeof item.content === 'string') // 文件类型先不处理

    // 存到数据库中
    await DanmuDB.saveDanmu(simplifiedData as Danmu[])
  }

  public static async getCourseInfoByCourseNo(course_no: number) {
    const courseInfo = await MoerAPI.getCurrentCourseInfo(course_no)

    if (courseInfo.code !== 0) {
      throw new Error(`获取课程信息失败: ${courseInfo.msg}`)
    }

    return courseInfo.data
  }

  public static async getCourseStartTimeByCourseNo(course_no: number) {
    const courseInfo = await MoerAPI.getCurrentCourseInfo(course_no)

    if (courseInfo.code !== 0) {
      throw new Error(`获取课程信息失败: ${courseInfo.msg}`)
    }

    return DateHelper.parseDate(courseInfo.data.startTime)
  }

  /**
   * 获取课程播放信息的通用方法
   * @param chat_id 聊天ID
   * @param course 课程信息
   * @returns 课程播放信息或null
   */
  private static async getCoursePlaybackInfo(chat_id: string, course: CourseInfo): Promise<CoursePlaybackInfo | null> {
    // 获取 moerId
    const moerId = await DataService.getMoerIdByChatId(chat_id)
    if (!moerId) {
      return null
    }

    // 获取课程映射
    const courseMap = await this.getMoerCourses(moerId)
    let courseStatus

    if (course.day === 0 || course.is_recording) {
      // 小课堂或录播课
      if (courseMap[course.day] && courseMap[course.day].record) {
        courseStatus = await this.getMoerCourseStatus(courseMap[course.day].record as ParsedResource)
      }
    } else {
      // 直播课
      if (courseMap[course.day] && courseMap[course.day].live) {
        const currentTime = await this.getCurrentTime(chat_id)
        const isCurrentDay = currentTime.is_course_week && currentTime.day === course.day

        // 如果是当天晚上，Polyv 直播回传到 Moer会很慢，直接去 Polyv 查询
        if (isCurrentDay) {
          const liveId = (courseMap[course.day].live as ParsedResource).liveId as number
          courseStatus = await this.pullMoerCourseStatusFromPolyv(moerId, liveId, new Date(), course.day)
        } else {
          courseStatus = await this.getMoerCourseStatus(courseMap[course.day].live as ParsedResource)
        }
      }
    }

    if (!courseStatus || ObjectUtil.isEmptyObject(courseStatus)) {
      return null
    }

    // 使用固定的课程时长，不再从API获取
    const courseDuration = this.getFixedCourseDuration(course.day)
    let playBackTime = courseStatus.playbackTime

    if (typeof playBackTime === 'string') {
      playBackTime = Number(playBackTime)
    }

    return {
      duration: courseDuration,
      playbackTime: playBackTime
    }
  }

  private static async pullMoerCourseStatusFromPolyv(userId: string, liveId: number, date: Date, day: number) {
    const viewLogs = await PolyvAPI.getViewLog(userId, DateHelper.formatDate(date, 'YYYY-MM-DD'), DateHelper.formatDate(date, 'YYYY-MM-DD'))
    let playbackTime = 0
    // 使用固定的课程时长
    const duration = this.getFixedCourseDuration(day)

    if (viewLogs.code !== 200) {
      return {
        duration: duration,
        playbackTime: playbackTime
      }
    }
    for (const content of viewLogs.data.contents) {
      if (content.channelId === liveId) {
        playbackTime += this.timeStringToSeconds(content.playDuration)
      }
    }

    return {
      duration: duration,
      playbackTime: playbackTime
    }
  }

  private static timeStringToSeconds(playDuration: string) {
    const timeParts = playDuration.split(':')
    let seconds = 0

    if (timeParts.length === 3) {
      // 格式为 HH:MM:SS
      const hours = parseInt(timeParts[0], 10)
      const minutes = parseInt(timeParts[1], 10)
      const secs = parseInt(timeParts[2], 10)

      seconds = hours * 3600 + minutes * 60 + secs
    } else if (timeParts.length === 2) {
      // 格式为 MM:SS
      const minutes = parseInt(timeParts[0], 10)
      const secs = parseInt(timeParts[1], 10)

      seconds = minutes * 60 + secs
    } else if (timeParts.length === 1) {
      // 格式为 SS
      seconds = parseInt(timeParts[0], 10)
    } else {
      throw new Error('无效的时间格式')
    }

    return seconds
  }

  static async isPaidSystemCourse(chatId: string) {
    if (ChatStateStore.getFlags(chatId).is_complete_payment) {
      return true
    }

    const moerId = await DataService.getMoerIdByChatId(chatId)
    if (!moerId) {
      return false
    }

    const courses =  await MoerAPI.getUserCourses(moerId)
    return courses.some((course) => course.type === 2)
  }

  static async isInAnyClass(chatId: string) {
    for (let i = 1; i <= 4; i++) {
      const isInLive = await this.isInClass(chatId, { day: i })
      if (isInLive) {
        return true
      }
      const isInRecord = await this.isInClass(chatId, { day: i, is_recording: false })
      if (isInRecord) {
        return true
      }
    }

    return false
  }

  // 计算原始周期号（不含任何“补偿”）
  private static getPeriod(date: Date): number {
    const startDate = dayjs('2024-01-29')        // 开营日（第 0 期）
    const startOfStartWeek = startDate.startOf('isoWeek')
    const startOfCurrentWeek = dayjs(date).startOf('isoWeek')

    // 原本你就减了 1，没动它
    return startOfCurrentWeek.diff(startOfStartWeek, 'week') - 1
  }

  // === 你调用的对外方法 ===
  static getCurrentWeekCourseNo(): number {
    try {
      // 使用新的本地假期配置计算期数
      const period = LocalHolidayConfig.getCurrentPeriod()
      logger.log('使用假期配置获取当前期数', { period })
      return period
    } catch (error) {
      logger.warn('使用假期配置计算期数失败，回退到原始方法', error)
      // 回退到原始计算方法
      return this.getCurrentWeekCourseNoLegacy()
    }
  }

  /**
   * 原始的期数计算方法（保留作为回退方案）
   */
  private static getCurrentWeekCourseNoLegacy(): number {
    const today = dayjs()
    dayjs.extend(isoWeek)
    dayjs.extend(isBetween)
    dayjs.extend(isSameOrAfter)

    /** 1. 4月26日‑5月4日：固定返回 63 期 */
    const freezeStart = dayjs('2025-04-21')
    const freezeEnd   = dayjs('2025-05-03')
    if (today.isBetween(freezeStart, freezeEnd, 'day', '[]')) {
      return 63
    }

    /** 2. 5月5日（含）之后：在原始值基础上 -2 */
    const resumeDate = dayjs('2025-05-04') // 64 期
    const base = this.getPeriod(today.toDate())
    if (today.isSameOrAfter(resumeDate)) {
      return base - 1
    }

    /** 3. 其余时间：按原规则返回 */
    return base
  }

  static getNextWeekCourseNo(): number {
    try {
      // 使用新的本地假期配置计算下一期期数
      const nextPeriod = LocalHolidayConfig.getNextPeriod()
      logger.log('使用假期配置获取下一期期数', { nextPeriod })
      return nextPeriod
    } catch (error) {
      logger.warn('使用假期配置计算下一期期数失败，回退到原始方法', error)
      // 回退到原始计算方法
      return this.getNextWeekCourseNoLegacy()
    }
  }

  /**
   * 原始的下一期期数计算方法（保留作为回退方案）
   */
  private static getNextWeekCourseNoLegacy(): number {
    dayjs.extend(isBetween)

    const today = dayjs()
    const freezeStart = dayjs('2025-04-21')
    const freezeEnd = dayjs('2025-05-03')

    // 停播区间内：下一期固定为 64 期
    if (today.isBetween(freezeStart, freezeEnd, 'day', '[]')) {
      return 64
    }

    if (today.isSame(dayjs('2025-05-04'), 'day')) {
      return 65
    }

    return DataService.getCurrentWeekCourseNo() + 1
  }

  /**
   * 检查当前是否在假期内
   * @returns 是否在假期内
   */
  static isCurrentlyInHoliday(): boolean {
    try {
      return LocalHolidayConfig.isCurrentlyInHoliday()
    } catch (error) {
      logger.warn('检查假期状态失败', error)
      return false // 默认不在假期内
    }
  }

  /**
   * 获取当前假期信息
   * @returns 假期信息，如果不在假期内则返回null
   */
  static getCurrentHolidayInfo(): {
    reason: string
    startDate: string
    endDate: string
  } | null {
    try {
      return LocalHolidayConfig.getCurrentHolidayInfo()
    } catch (error) {
      logger.warn('获取假期信息失败', error)
      return null
    }
  }

  /**
   * 检查指定日期是否在假期内
   * @param date 检查日期
   * @returns 是否在假期内
   */
  static isDateInHoliday(date: Date): boolean {
    try {
      return LocalHolidayConfig.isDateInHoliday(date)
    } catch (error) {
      logger.warn('检查指定日期假期状态失败', error)
      return false
    }
  }

  static async getWxIdByMoerId(moerId: string) {
    const chat =  await ChatDB.getByMoerId(moerId)

    if (!chat) {
      return null
    }

    return chat.wx_id
  }

  /**
   * 粗筛，有可能有重名的客户，不要用在生产环境！！！！！
   * @param name
   */
  static async getChatByWechatName(name: string) {
    return PrismaMongoClient.getInstance().chat.findRaw({
      filter: {
        'contact.wx_name': name
      }
    })
  }

  static async getCourseNoByChatId(chatId: string) {
    const chat = await ChatDB.getById(chatId)
    if (!chat) {
      return null
    }

    return chat.course_no
  }

  public static tagsMap = new Map<string, string>() // tagName => tagId

  /**
   * 更新客户的微信标签
   */
  public static async updateTags(userId: string, tag: WecomTag | string) {
    try {
      const externalId = await JuziAPI.wxIdToExternalUserId(userId)
      if (!externalId) {
        logger.error('获取 ExternalUserId 失败', userId, Config.setting.wechatConfig?.id as string)
        return
      }

      if (this.tagsMap.size === 0) {
        // 拉取标签，更新上去
        const tagsGroups = await JuziAPI.getTags()
        if (tagsGroups) {
          tagsGroups.forEach((group) => {
            group.tags.forEach((tag) => {
              this.tagsMap.set(tag.name, tag.tagId)
            })
          })
        }
      }

      if (!this.tagsMap.has(tag)) {
        throw new Error(`找不到对应的标签：${tag}`)
      }

      const tagId = this.tagsMap.get(tag)
      if (!tagId) {
        throw new Error(`找不到标签：${  tag}`)
      }

      await JuziAPI.updateUserTags(externalId, Config.setting.wechatConfig?.botUserId as string, [tagId], [])
    } catch (e) {
      logger.error('更新客户标签失败', e)
    }
  }

  public static async deleteTags(userId: string, tag: WecomTag | string) {
    try {
      const externalId = await JuziAPI.wxIdToExternalUserId(userId)
      if (!externalId) {
        logger.error('获取 ExternalUserId 失败', userId, Config.setting.wechatConfig?.id as string)
        return
      }

      if (this.tagsMap.size === 0) {
        // 拉取标签，更新上去
        const tagsGroups = await JuziAPI.getTags()
        if (tagsGroups) {
          tagsGroups.forEach((group) => {
            group.tags.forEach((tag) => {
              this.tagsMap.set(tag.name, tag.tagId)
            })
          })
        }
      }

      if (!this.tagsMap.has(tag)) {
        throw new Error(`找不到对应的标签：${tag}`)
      }

      const tagId = this.tagsMap.get(tag)
      if (!tagId) {
        throw new Error(`找不到标签：${  tag}`)
      }

      await JuziAPI.updateUserTags(externalId, Config.setting.wechatConfig?.botUserId as string, [], [tagId])
    } catch (e) {
      logger.error('删除客户标签失败', e)
    }
  }

  public static async isWithinClassTime(currentTime: IScheduleTime) {
    // 定义每一天的上课结束时间，提前 10 分钟
    const dayEndTimeMap = {
      1: '21:35:00', // 周一
      2: '21:05:00', // 周二
      3: '21:40:00', // 周三
      4: '22:05:00', // 周四
    }

    // 检查是否为上课周
    if (!currentTime.is_course_week) {
      return false
    }

    const currentDay = currentTime.day

    // 检查是否在星期一到星期四
    if (currentDay < 1 || currentDay > 4) {
      return false
    }

    // 获取当前时间
    const currentTimeStr = currentTime.time

    // 定义上课开始时间和当天结束时间
    const classStartTime = '20:05:00'
    const classEndTime = dayEndTimeMap[currentDay]

    // 检查当前时间是否在上课开始时间之后或等于，并且在上课结束时间之前或等于
    const afterStart = DateHelper.isTimeAfter(currentTimeStr, classStartTime) || currentTimeStr === classStartTime
    const beforeEnd = DateHelper.isTimeBefore(currentTimeStr, classEndTime) || currentTimeStr === classEndTime

    return afterStart && beforeEnd
  }

  /**
   * 是否曾经买过入门营的课程，注意调用前要先检查客户是否购买过课程
   */
  public static async hasPurchasedIntroCourseBefore(moerId: string) {
    // 查询课程
    const userCourses = await MoerAPI.getUserCourses(moerId)
    let introCourseCount = 0

    for (const userCourse of userCourses) {
      if (userCourse.sid === 1) {
        if (userCourse.is_refund === 1) {
          continue
        }

        introCourseCount ++

        // if (userCourse.stage < DataService.getCurrentWeekCourseNo()) {
        //   return true
        // }
      }
    }

    return introCourseCount > 1
  }

  static async getSystemCourseStartTime(chatId: string) {
    // 获取 MoerId
    const moerId = await DataService.getMoerIdByChatId(chatId)
    if (!moerId) {
      return ''
    }

    // 查询课程
    const courses = await MoerAPI.getUserCourses(moerId)
    const systemCourse = courses.find((course) => course.type === 2)

    if (!systemCourse) {
      const systemCourseInfo = await MoerAPI.getSystemCourseInfo()
      return systemCourseInfo.start_time
    }

    return systemCourse.start_time
  }

  static async getChatsByCourseNo(courseNo: number) {
    const chats = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        course_no: courseNo,
      }
    })

    return chats.filter((chat) => !Config.isInternalMember(chat.contact.wx_id) && !chat.id.includes('R') && !chat.id.includes('local'))
  }

  public static async findPhoneNumber(userId: string) {
    logger.trace(`findPhoneNumber userId: ${userId}`)

    let phoneNumber = ChatStateStore.get(getChatId(userId)).userSlots.phoneNumber
    if (phoneNumber) {
      return phoneNumber
    }

    phoneNumber = await this.findPhoneNumberByRemark(userId)
    if (phoneNumber) {
      return phoneNumber
    }

    phoneNumber = await this.findPhoneNumberByExternalId(userId)
    if (phoneNumber) {
      return phoneNumber
    }

    return ''
  }

  private static async findPhoneNumberByExternalId(userId: string) {
    // 1. externalId 查询 CRM
    const externalId = await JuziAPI.wxIdToExternalUserId(userId)
    if (!externalId) {
      return ''
    }

    // 查询 Redis 存储
    const redis = new RedisCacheDB(externalId)
    const userInfo = await redis.get()
    if (userInfo) {
      logger.trace({ chat_id: getChatId(userId) }, '从推送事件中查找到手机号')
      return userInfo.mobile
    }

    const [error, data] = await catchError(MoerAPI.getUserPhone({ externalUserId: externalId }))
    if (error) {
      return ''
    }

    if (data.mobile === '19119281868') {
      return ''
    }

    if (!data.mobile) {
      return ''
    }

    logger.trace({ chat_id: getChatId(userId) }, '从 Moer API 中查找到手机号', data.mobile)
    return data.mobile
  }

  private static async findPhoneNumberByRemark(userId: string) {
    // 读取备注中是否包含电话号码
    const phoneNumber = await JuziAPI.getCustomerPhoneNumber(Config.setting.wechatConfig?.id as string, userId)
    if (phoneNumber && phoneNumber.length >= 1 && phoneNumber.some((phone) => RegexHelper.extractPhoneNumber(phone))) {
      const phone = phoneNumber.find((phone) => RegexHelper.extractPhoneNumber(phone)) as string
      const [error, moerUser] = await catchError(MoerAPI.getUserByPhone(phone))
      if (error) {
        return ''
      }

      logger.trace({ chat_id: getChatId(userId) }, '备注已绑定手机号')
      return phone
    }

    return ''
  }
}