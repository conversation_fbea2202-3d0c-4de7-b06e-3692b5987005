/**
 * 暂停日历模型使用示例和迁移指南
 *
 * 这个文件展示了如何使用新的暂停日历模型来管理课程期数计算
 */

import { PauseCalendar } from './pause_calendar'
import { CoursePeriodCalculator } from './course_period_calculator'
import { DataService } from '../../getter/getData'
import dayjs from 'dayjs'

/**
 * 示例1：添加假期时间段
 */
export async function addHolidayExample() {
  console.log('=== 添加假期时间段示例 ===')

  // 添加五一假期：2025年5月1日-5月3日
  const mayDayHoliday = await PauseCalendar.addPause(
    new Date('2025-05-01'),
    new Date('2025-05-03'),
    '五一劳动节假期'
  )

  console.log('添加五一假期成功:', {
    id: mayDayHoliday.id,
    startDate: dayjs(mayDayHoliday.start_date).format('YYYY-MM-DD'),
    endDate: dayjs(mayDayHoliday.end_date).format('YYYY-MM-DD'),
    reason: mayDayHoliday.reason
  })

  // 添加国庆假期：2025年10月1日-10月7日
  const nationalDayHoliday = await PauseCalendar.addPause(
    new Date('2025-10-01'),
    new Date('2025-10-07'),
    '国庆节假期'
  )

  console.log('添加国庆假期成功:', {
    id: nationalDayHoliday.id,
    startDate: dayjs(nationalDayHoliday.start_date).format('YYYY-MM-DD'),
    endDate: dayjs(nationalDayHoliday.end_date).format('YYYY-MM-DD'),
    reason: nationalDayHoliday.reason
  })
}

/**
 * 示例2：查询和管理假期
 */
export async function manageHolidaysExample() {
  console.log('=== 查询和管理假期示例 ===')

  // 查询所有假期
  const allHolidays = await PauseCalendar.getAllPauses()
  console.log('所有假期:', allHolidays.map((h) => ({
    id: h.id,
    startDate: dayjs(h.start_date).format('YYYY-MM-DD'),
    endDate: dayjs(h.end_date).format('YYYY-MM-DD'),
    reason: h.reason
  })))

  // 查询与特定时间段重叠的假期
  const overlapping = await PauseCalendar.getOverlappingPauses(
    new Date('2025-05-01'),
    new Date('2025-05-10')
  )
  console.log('5月1日-10日期间的假期:', overlapping.length)

  // 删除特定假期（需要假期ID）
  if (allHolidays.length > 0) {
    const deleted = await PauseCalendar.removePause(allHolidays[0].id!)
    console.log('删除假期结果:', deleted)
  }
}

/**
 * 示例3：期数计算
 */
export async function periodCalculationExample() {
  console.log('=== 期数计算示例 ===')

  // 获取当前期数
  const currentPeriod = await CoursePeriodCalculator.getCurrentPeriod()
  console.log('当前期数:', currentPeriod)

  // 获取下一期期数
  const nextPeriod = await CoursePeriodCalculator.getNextPeriod()
  console.log('下一期期数:', nextPeriod)

  // 计算特定日期的期数
  const specificPeriod = await CoursePeriodCalculator.calculatePeriod(new Date('2025-06-01'))
  console.log('2025年6月1日的期数:', specificPeriod)

  // 获取基准配置信息
  const anchorInfo = CoursePeriodCalculator.getAnchorInfo()
  console.log('基准配置:', anchorInfo)
}

/**
 * 示例4：假期状态检查
 */
export async function holidayStatusExample() {
  console.log('=== 假期状态检查示例 ===')

  // 检查当前是否在假期内
  const isCurrentlyInHoliday = await DataService.isCurrentlyInHoliday()
  console.log('当前是否在假期内:', isCurrentlyInHoliday)

  if (isCurrentlyInHoliday) {
    // 获取当前假期信息
    const holidayInfo = await DataService.getCurrentHolidayInfo()
    console.log('当前假期信息:', holidayInfo)
  }

  // 检查特定日期是否在假期内
  const isSpecificDateInHoliday = await DataService.isDateInHoliday(new Date('2025-05-01'))
  console.log('2025年5月1日是否在假期内:', isSpecificDateInHoliday)
}

/**
 * 示例5：期数预览功能
 */
export async function periodPreviewExample() {
  console.log('=== 期数预览示例 ===')

  // 预览一周的期数计算结果
  const preview = await CoursePeriodCalculator.previewPeriods(
    new Date('2025-05-01'),
    new Date('2025-05-07')
  )

  console.log('2025年5月1日-7日期数预览:')
  preview.forEach((p) => {
    console.log(`${p.date}: 第${p.period}期 ${p.isPaused ? '(假期)' : ''}`)
  })
}

/**
 * 示例6：在业务逻辑中使用假期检查
 */
export async function businessLogicExample() {
  console.log('=== 业务逻辑中的假期检查示例 ===')

  // 在发送消息前检查是否在假期内
  const isInHoliday = await DataService.isCurrentlyInHoliday()

  if (isInHoliday) {
    const holidayInfo = await DataService.getCurrentHolidayInfo()
    console.log(`当前在假期内(${holidayInfo?.reason})，暂停发送营销消息`)
    return
  }

  console.log('不在假期内，可以正常发送消息')

  // 在计划任务时检查假期
  const tomorrow = dayjs().add(1, 'day').toDate()
  const isTomorrowHoliday = await DataService.isDateInHoliday(tomorrow)

  if (isTomorrowHoliday) {
    console.log('明天是假期，延后执行任务')
    // 寻找下一个非假期日期
    const nextWorkDay = await CoursePeriodCalculator.getNextNonPausedDate(tomorrow)
    console.log('下一个工作日:', dayjs(nextWorkDay).format('YYYY-MM-DD'))
  } else {
    console.log('明天不是假期，按计划执行任务')
  }
}

/**
 * 迁移指南：从硬编码假期到暂停日历模型
 */
export async function migrationGuide() {
  console.log('=== 迁移指南 ===')

  // 步骤1：添加现有的硬编码假期到暂停日历
  console.log('步骤1：迁移现有假期数据')

  // 迁移现有的4月21日-5月3日假期
  await PauseCalendar.addPause(
    new Date('2025-04-21'),
    new Date('2025-05-03'),
    '原硬编码假期迁移'
  )

  console.log('已迁移原有假期数据')

  // 步骤2：使用新的异步API
  console.log('步骤2：使用新的异步API')

  // 旧方式（同步）
  const oldCurrentPeriod = DataService.getCurrentWeekCourseNo()
  console.log('旧方式获取当前期数:', oldCurrentPeriod)

  // 新方式（异步，推荐）
  const newCurrentPeriod = await DataService.getCurrentWeekCourseNoAsync()
  console.log('新方式获取当前期数:', newCurrentPeriod)

  // 步骤3：在业务逻辑中添加假期检查
  console.log('步骤3：在业务逻辑中添加假期检查')

  // 示例：在发送SOP消息前检查假期
  async function sendSOPMessage() {
    const isInHoliday = await DataService.isCurrentlyInHoliday()
    if (isInHoliday) {
      console.log('假期期间，跳过SOP消息发送')
      return
    }

    console.log('正常发送SOP消息')
    // 实际的消息发送逻辑...
  }

  await sendSOPMessage()
}

/**
 * 运行所有示例
 */
export async function runAllExamples() {
  try {
    await addHolidayExample()
    await manageHolidaysExample()
    await periodCalculationExample()
    await holidayStatusExample()
    await periodPreviewExample()
    await businessLogicExample()
    await migrationGuide()
  } catch (error) {
    console.error('运行示例时出错:', error)
  }
}

// 如果直接运行此文件，执行所有示例
if (require.main === module) {
  runAllExamples()
}
