import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import logger from '../../../../model/logger/logger'
import minmax from 'dayjs/plugin/minMax'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'

dayjs.extend(isBetween)

export interface HolidayPeriod {
  startDate: string  // 格式: 'YYYY-MM-DD'
  endDate: string    // 格式: 'YYYY-MM-DD'
  reason?: string    // 假期原因
}

/**
 * 本地假期配置
 * 业务人员只需在这里添加假期时间段，系统会自动处理期数计算
 */
export class LocalHolidayConfig {
  /**
   * 假期配置列表
   * 添加新假期时，只需在这个数组中添加新的时间段即可
   */
  private static readonly HOLIDAY_PERIODS: HolidayPeriod[] = [
    // 现有的假期（从硬编码迁移）
    {
      startDate: '2025-04-21',
      endDate: '2025-05-03',
      reason: '五一假期'
    },
    {
      startDate: '2025-10-01',
      endDate: '2025-10-05',
      reason: '国庆节假期'
    }

    // 示例：添加新假期的方法
    // {
    //   startDate: '2025-05-01',
    //   endDate: '2025-05-03',
    //   reason: '五一劳动节假期'
    // },
    // {
    //   startDate: '2025-10-01',
    //   endDate: '2025-10-07',
    //   reason: '国庆节假期'
    // }
  ]

  /**
   * 基准配置
   */
  private static readonly ANCHOR_DATE = dayjs('2024-01-29') // 第0期开始日期
  private static readonly ANCHOR_PERIOD = 0 // 基准期数
  private static readonly PERIOD_DURATION = 7 // 每期天数

  /**
   * 检查指定日期是否在假期内
   * @param date 检查日期
   * @returns 是否在假期内
   */
  public static isDateInHoliday(date: Date): boolean {
    const checkDate = dayjs(date)

    return this.HOLIDAY_PERIODS.some((holiday) => {
      return checkDate.isBetween(
        dayjs(holiday.startDate),
        dayjs(holiday.endDate),
        'day',
        '[]' // 包含边界
      )
    })
  }

  /**
   * 检查当前是否在假期内
   * @returns 是否在假期内
   */
  public static isCurrentlyInHoliday(): boolean {
    return this.isDateInHoliday(new Date())
  }

  /**
   * 获取当前假期信息（如果在假期内）
   * @returns 假期信息，如果不在假期内则返回null
   */
  public static getCurrentHolidayInfo(): {
    reason: string
    startDate: string
    endDate: string
  } | null {
    const today = dayjs()

    const currentHoliday = this.HOLIDAY_PERIODS.find((holiday) => {
      return today.isBetween(
        dayjs(holiday.startDate),
        dayjs(holiday.endDate),
        'day',
        '[]'
      )
    })

    if (!currentHoliday) {
      return null
    }

    return {
      reason: currentHoliday.reason || '假期',
      startDate: currentHoliday.startDate,
      endDate: currentHoliday.endDate
    }
  }

  /**
   * 计算有效天数（扣除假期时间段）
   * @param anchorDate 基准日期
   * @param queryDate 查询日期
   * @returns 有效天数
   */
  public static calculateEffectiveDays(anchorDate: Date, queryDate: Date): number {
    const anchor = dayjs(anchorDate)
    const query = dayjs(queryDate)
    dayjs.extend(minmax)
    dayjs.extend(isSameOrBefore)

    // 确保日期顺序正确
    if (anchor.isAfter(query)) {
      logger.warn('基准日期晚于查询日期', {
        anchorDate: anchor.format('YYYY-MM-DD'),
        queryDate: query.format('YYYY-MM-DD')
      })
      return 0
    }

    // 计算总天数（包含首尾）
    const totalDays = query.diff(anchor, 'day') + 1

    // 计算被假期暂停的天数
    let pausedDays = 0

    for (const holiday of this.HOLIDAY_PERIODS) {
      const holidayStart = dayjs(holiday.startDate)
      const holidayEnd = dayjs(holiday.endDate)

      // 计算假期与查询时间段的交集
      const intersectionStart = dayjs.max(anchor, holidayStart)!
      const intersectionEnd = dayjs.min(query, holidayEnd)!

      // 如果有交集，计算交集天数
      if (intersectionStart.isSameOrBefore(intersectionEnd)) {
        const intersectionDays = intersectionEnd.diff(intersectionStart, 'day') + 1
        pausedDays += intersectionDays
      }
    }

    const effectiveDays = totalDays - pausedDays

    logger.log('计算有效天数', {
      anchorDate: anchor.format('YYYY-MM-DD'),
      queryDate: query.format('YYYY-MM-DD'),
      totalDays,
      pausedDays,
      effectiveDays,
      holidayCount: this.HOLIDAY_PERIODS.length
    })

    return Math.max(0, effectiveDays) // 确保不返回负数
  }

  /**
   * 计算指定日期的课程期数
   * @param queryDate 查询日期
   * @returns 期数
   */
  public static calculatePeriod(queryDate: Date): number {
    const anchorDate = this.ANCHOR_DATE.toDate()
    const query = dayjs(queryDate)

    // 如果查询日期早于基准日期，返回0
    if (query.isBefore(this.ANCHOR_DATE)) {
      logger.warn('查询日期早于基准日期', {
        queryDate: query.format('YYYY-MM-DD'),
        anchorDate: this.ANCHOR_DATE.format('YYYY-MM-DD')
      })
      return 0
    }

    // 计算有效天数
    const effectiveDays = this.calculateEffectiveDays(anchorDate, queryDate)

    // 计算期数
    // 公式：基准期数 + floor((有效天数 - 1) / 每期时长)
    const period = this.ANCHOR_PERIOD + Math.floor((effectiveDays - 1) / this.PERIOD_DURATION)

    logger.log('计算课程期数', {
      queryDate: query.format('YYYY-MM-DD'),
      effectiveDays,
      period,
      anchorDate: this.ANCHOR_DATE.format('YYYY-MM-DD'),
      anchorPeriod: this.ANCHOR_PERIOD,
      periodDuration: this.PERIOD_DURATION
    })

    return Math.max(0, period) // 确保不返回负数
  }

  /**
   * 获取当前期数
   * @returns 当前期数
   */
  public static getCurrentPeriod(): number {
    return this.calculatePeriod(new Date())
  }

  /**
   * 获取下一期期数
   * @returns 下一期期数
   */
  public static getNextPeriod(): number {
    const currentPeriod = this.getCurrentPeriod()
    return currentPeriod + 1
  }

  /**
   * 获取所有假期配置（用于调试）
   * @returns 假期配置列表
   */
  public static getAllHolidays(): HolidayPeriod[] {
    return [...this.HOLIDAY_PERIODS] // 返回副本，防止外部修改
  }

  /**
   * 获取基准配置信息
   */
  public static getAnchorInfo() {
    return {
      anchorDate: this.ANCHOR_DATE.format('YYYY-MM-DD'),
      anchorPeriod: this.ANCHOR_PERIOD,
      periodDuration: this.PERIOD_DURATION
    }
  }

  /**
   * 预览期数计算结果（用于调试）
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @returns 期数预览列表
   */
  public static previewPeriods(startDate: Date, endDate: Date): Array<{
    date: string
    period: number
    isHoliday: boolean
  }> {
    dayjs.extend(isSameOrBefore)

    const results: Array<{ date: string; period: number; isHoliday: boolean }> = []
    let currentDate = dayjs(startDate)
    const end = dayjs(endDate)

    while (currentDate.isSameOrBefore(end)) {
      const period = this.calculatePeriod(currentDate.toDate())
      const isHoliday = this.isDateInHoliday(currentDate.toDate())

      results.push({
        date: currentDate.format('YYYY-MM-DD'),
        period,
        isHoliday
      })

      currentDate = currentDate.add(1, 'day')
    }

    return results
  }
}
