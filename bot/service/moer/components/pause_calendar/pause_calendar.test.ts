import { PauseCalendar } from './pause_calendar'
import { CoursePeriodCalculator } from './course_period_calculator'
import { DataService } from '../../getter/getData'
import dayjs from 'dayjs'

describe('PauseCalendar 暂停日历测试', () => {
  beforeEach(async () => {
    // 清空所有暂停记录
    await PauseCalendar.clearAllPauses()
  })

  afterAll(async () => {
    // 测试结束后清理
    await PauseCalendar.clearAllPauses()
  })

  describe('基础功能测试', () => {
    it('应该能够添加暂停时间段', async () => {
      const startDate = new Date('2025-01-08')
      const endDate = new Date('2025-01-08')
      const reason = '测试假期'

      const pause = await PauseCalendar.addPause(startDate, endDate, reason)

      expect(pause.start_date).toEqual(startDate)
      expect(pause.end_date).toEqual(endDate)
      expect(pause.reason).toBe(reason)
      expect(pause.id).toBeDefined()
    })

    it('应该能够删除暂停时间段', async () => {
      const startDate = new Date('2025-01-08')
      const endDate = new Date('2025-01-08')
      
      const pause = await PauseCalendar.addPause(startDate, endDate, '测试假期')
      const deleted = await PauseCalendar.removePause(pause.id!)

      expect(deleted).toBe(true)

      const allPauses = await PauseCalendar.getAllPauses()
      expect(allPauses).toHaveLength(0)
    })

    it('应该能够查询重叠的暂停时间段', async () => {
      // 添加一个暂停时间段：1月8日-1月10日
      await PauseCalendar.addPause(new Date('2025-01-08'), new Date('2025-01-10'), '假期1')
      
      // 查询与1月9日-1月11日重叠的时间段
      const overlapping = await PauseCalendar.getOverlappingPauses(
        new Date('2025-01-09'), 
        new Date('2025-01-11')
      )

      expect(overlapping).toHaveLength(1)
      expect(overlapping[0].reason).toBe('假期1')
    })
  })

  describe('有效天数计算测试', () => {
    it('应该正确计算无暂停时的有效天数', async () => {
      const anchorDate = new Date('2025-01-06') // 周一
      const queryDate = new Date('2025-01-12')  // 下周日

      const effectiveDays = await PauseCalendar.calculateEffectiveDays(anchorDate, queryDate)
      
      // 从1月6日到1月12日，包含首尾，应该是7天
      expect(effectiveDays).toBe(7)
    })

    it('应该正确计算有暂停时的有效天数', async () => {
      const anchorDate = new Date('2025-01-06') // 周一
      const queryDate = new Date('2025-01-12')  // 下周日

      // 添加1月8日（周三）暂停1天
      await PauseCalendar.addPause(new Date('2025-01-08'), new Date('2025-01-08'), '测试暂停')

      const effectiveDays = await PauseCalendar.calculateEffectiveDays(anchorDate, queryDate)
      
      // 总共7天，减去1天暂停，应该是6天
      expect(effectiveDays).toBe(6)
    })

    it('应该正确处理多个暂停时间段', async () => {
      const anchorDate = new Date('2025-01-06') // 周一
      const queryDate = new Date('2025-01-20')  // 两周后的周一

      // 添加两个暂停时间段
      await PauseCalendar.addPause(new Date('2025-01-08'), new Date('2025-01-08'), '暂停1') // 1天
      await PauseCalendar.addPause(new Date('2025-01-15'), new Date('2025-01-16'), '暂停2') // 2天

      const effectiveDays = await PauseCalendar.calculateEffectiveDays(anchorDate, queryDate)
      
      // 总共15天（1月6日到1月20日），减去3天暂停，应该是12天
      expect(effectiveDays).toBe(12)
    })
  })

  describe('期数计算测试', () => {
    it('应该正确计算无暂停时的期数', async () => {
      // 基准日期是2024-01-29，第0期
      // 2025-01-06是第48周，应该是第47期（48-1）
      const period = await CoursePeriodCalculator.calculatePeriod(new Date('2025-01-06'))
      
      // 验证期数计算逻辑
      expect(period).toBeGreaterThanOrEqual(0)
    })

    it('应该正确处理假期对期数的影响', async () => {
      // 添加1月8日暂停1天
      await PauseCalendar.addPause(new Date('2025-01-08'), new Date('2025-01-08'), '测试假期')

      const periodBefore = await CoursePeriodCalculator.calculatePeriod(new Date('2025-01-07')) // 假期前
      const periodDuring = await CoursePeriodCalculator.calculatePeriod(new Date('2025-01-08')) // 假期中
      const periodAfter = await CoursePeriodCalculator.calculatePeriod(new Date('2025-01-14'))  // 假期后一周

      // 假期中的期数应该与假期前相同（期数被冻结）
      expect(periodDuring).toBe(periodBefore)
      
      // 假期后的期数应该体现顺延效果
      expect(periodAfter).toBeGreaterThan(periodBefore)
    })
  })

  describe('假期状态检查测试', () => {
    it('应该正确检查当前是否在假期内', async () => {
      // 添加今天的假期
      const today = new Date()
      await PauseCalendar.addPause(today, today, '今日假期')

      const isInHoliday = await CoursePeriodCalculator.isCurrentlyInHoliday()
      expect(isInHoliday).toBe(true)

      const holidayInfo = await CoursePeriodCalculator.getCurrentHolidayInfo()
      expect(holidayInfo).not.toBeNull()
      expect(holidayInfo!.reason).toBe('今日假期')
    })

    it('不在假期时应该返回false', async () => {
      // 不添加任何假期
      const isInHoliday = await CoursePeriodCalculator.isCurrentlyInHoliday()
      expect(isInHoliday).toBe(false)

      const holidayInfo = await CoursePeriodCalculator.getCurrentHolidayInfo()
      expect(holidayInfo).toBeNull()
    })
  })

  describe('DataService 集成测试', () => {
    it('DataService 应该能够检查假期状态', async () => {
      // 添加今天的假期
      const today = new Date()
      await PauseCalendar.addPause(today, today, 'DataService测试假期')

      const isInHoliday = await DataService.isCurrentlyInHoliday()
      expect(isInHoliday).toBe(true)

      const holidayInfo = await DataService.getCurrentHolidayInfo()
      expect(holidayInfo).not.toBeNull()
      expect(holidayInfo!.reason).toBe('DataService测试假期')
    })

    it('DataService 应该能够检查指定日期的假期状态', async () => {
      const testDate = new Date('2025-01-15')
      await PauseCalendar.addPause(testDate, testDate, '指定日期假期')

      const isInHoliday = await DataService.isDateInHoliday(testDate)
      expect(isInHoliday).toBe(true)

      const notHolidayDate = new Date('2025-01-16')
      const isNotInHoliday = await DataService.isDateInHoliday(notHolidayDate)
      expect(isNotInHoliday).toBe(false)
    })
  })

  describe('期数预览功能测试', () => {
    it('应该能够预览期数计算结果', async () => {
      // 添加假期
      await PauseCalendar.addPause(new Date('2025-01-08'), new Date('2025-01-09'), '预览测试假期')

      const preview = await CoursePeriodCalculator.previewPeriods(
        new Date('2025-01-06'),
        new Date('2025-01-12')
      )

      expect(preview).toHaveLength(7) // 7天的预览
      
      // 检查假期日期被正确标记
      const jan8 = preview.find(p => p.date === '2025-01-08')
      const jan9 = preview.find(p => p.date === '2025-01-09')
      
      expect(jan8?.isPaused).toBe(true)
      expect(jan9?.isPaused).toBe(true)
    })
  })
}, 60000) // 设置60秒超时
